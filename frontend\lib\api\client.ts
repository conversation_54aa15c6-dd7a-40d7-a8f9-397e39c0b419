// Note: This implementation uses native fetch instead of axios to avoid dependency issues
// You can replace this with axios later if needed

import type { ApiError } from '@/lib/types/auth'
import { getErrorMessage } from '@/lib/utils/error-messages'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

class ApiClient {
  private baseURL: string

  constructor() {
    this.baseURL = API_BASE_URL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    _retry = true
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(options.headers || {}),
      },
      credentials: 'include', // Important: include cookies for authentication
      ...options,
    }

    try {
      const response = await fetch(url, config)

      // 优先尝试解析JSON（无论状态码）
      let result: any = null
      try {
        result = await response.json()
      } catch {
        // ignore parse error; result 可能为 null
      }
      
      // 非200状态码处理（包含HTTP 401）
      if (!response.ok) {
        if (response.status === 401 && _retry && endpoint !== '/auth/refresh') {
          const refreshed = await this.refreshAccessToken()
          if (refreshed) {
            return this.request<T>(endpoint, options, false)
          }
          if (typeof window !== 'undefined') {
            window.location.href = '/'
          }
        }
        if (result && result.msg) {
          throw new Error(getErrorMessage(result.msg) || `HTTP error! status: ${response.status}`)
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 业务包装：即使是200响应，也需要检查code
      if (result && typeof result.code !== 'undefined') {
        if (result.code === 0) {
          return result as T
        }
        if (result.code === 401 && _retry && endpoint !== '/auth/refresh') {
          const refreshed = await this.refreshAccessToken()
          if (refreshed) {
            return this.request<T>(endpoint, options, false)
          }
          if (typeof window !== 'undefined') {
            window.location.href = '/'
          }
          throw new Error(getErrorMessage(result.msg) || '请先登录')
        }
        throw new Error(getErrorMessage(result.msg) || '请求失败')
      }

      // 无包装时，直接返回解析结果
      return result as T
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('登录已过期') ||
            error.message.includes('请先登录') ||
            error.message.includes('401') ||
            error.message.includes('Unauthorized')) {
          // Token expired, redirect to login page
          if (typeof window !== 'undefined') {
            window.location.href = '/'
          }
        }
        throw error
      }
      throw new Error('Network error occurred')
    }
  }

  async get<T>(endpoint: string, config: RequestInit = {}) {
    return this.request<T>(endpoint, { ...config, method: 'GET' })
  }

  async post<T>(endpoint: string, data = {}, config: RequestInit = {}) {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async put<T>(endpoint: string, data = {}, config: RequestInit = {}) {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async delete<T>(endpoint: string, config: RequestInit = {}) {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' })
  }

  // 静默刷新：仅调用一次，成功则更新 httpOnly cookie_access
  private async refreshAccessToken(): Promise<boolean> {
    try {
      const url = `${this.baseURL}/auth/refresh`
      const resp = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      })
      let data: any = null
      try {
        data = await resp.json()
      } catch {
        // ignore parse error
      }
      return !!(resp.ok && data && data.code === 0)
    } catch {
      return false
    }
  }
}

export const apiClient = new ApiClient()