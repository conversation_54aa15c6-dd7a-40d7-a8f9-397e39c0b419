import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const token = request.cookies.get('cookie_access')?.value
  const isAuthPage = request.nextUrl.pathname === '/'
  
  // Get the pathname of the request
  const { pathname } = request.nextUrl

  // Define public paths that don't require authentication
  const publicPaths = ['/']
  
  // Check if the current path is public
  const isPublicPath = publicPaths.includes(pathname)
  
  // If user is not authenticated and trying to access a protected route
  if (!token && !isPublicPath) {
    return NextResponse.redirect(new URL('/', request.url))
  }
  
  // If user is authenticated and trying to access login page
  if (token && isAuthPage) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}