/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./app/dashboard/loading.tsx":
/*!***********************************!*\
  !*** ./app/dashboard/loading.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQTtJQUN0QixPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGRhc2hib2FyZFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\my_project\\work\\hermes\\frontend\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17b65432e590\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTdiNjU0MzJlNTkwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3/node_modules/geist/dist/sans.js\");\n/* harmony import */ var geist_font_mono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! geist/font/mono */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3/node_modules/geist/dist/mono.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./contexts/auth-context.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: '智能客服引擎系统',\n    description: 'Created with v0',\n    generator: 'v0.app'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\nhtml {\n  font-family: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.style.fontFamily};\n  --font-sans: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable};\n  --font-mono: ${geist_font_mono__WEBPACK_IMPORTED_MODULE_2__.GeistMono.variable};\n}\n        `\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDMkM7QUFDQTtBQUNXO0FBQ2hDO0FBRWYsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxXQUFXO0FBQ2IsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7MEJBQ0MsNEVBQUNDOzhCQUFPLENBQUM7O2VBRUYsRUFBRVosc0RBQVNBLENBQUNZLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO2VBQzdCLEVBQUViLHNEQUFTQSxDQUFDYyxRQUFRLENBQUM7ZUFDckIsRUFBRWIsc0RBQVNBLENBQUNhLFFBQVEsQ0FBQzs7UUFFNUIsQ0FBQzs7Ozs7Ozs7Ozs7MEJBRUgsOERBQUNDOzBCQUNDLDRFQUFDYixnRUFBWUE7OEJBQ1ZNOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcbXlfcHJvamVjdFxcd29ya1xcaGVybWVzXFxmcm9udGVuZFxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgR2Vpc3RTYW5zIH0gZnJvbSAnZ2Vpc3QvZm9udC9zYW5zJ1xuaW1wb3J0IHsgR2Vpc3RNb25vIH0gZnJvbSAnZ2Vpc3QvZm9udC9tb25vJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9hdXRoLWNvbnRleHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5pm66IO95a6i5pyN5byV5pOO57O757ufJyxcbiAgZGVzY3JpcHRpb246ICdDcmVhdGVkIHdpdGggdjAnLFxuICBnZW5lcmF0b3I6ICd2MC5hcHAnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPHN0eWxlPntgXG5odG1sIHtcbiAgZm9udC1mYW1pbHk6ICR7R2Vpc3RTYW5zLnN0eWxlLmZvbnRGYW1pbHl9O1xuICAtLWZvbnQtc2FuczogJHtHZWlzdFNhbnMudmFyaWFibGV9O1xuICAtLWZvbnQtbW9ubzogJHtHZWlzdE1vbm8udmFyaWFibGV9O1xufVxuICAgICAgICBgfTwvc3R5bGU+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkdlaXN0U2FucyIsIkdlaXN0TW9ubyIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdlbmVyYXRvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsInN0eWxlIiwiZm9udEZhbWlseSIsInZhcmlhYmxlIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\my_project\\work\\hermes\\frontend\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\my_project\\work\\hermes\\frontend\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?df05\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/loading.tsx */ \"(rsc)/./app/dashboard/loading.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module4, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2U9JTJGZGFzaGJvYXJkJTJGcGFnZSZhcHBQYXRocz0lMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNteV9wcm9qZWN0JTVDd29yayU1Q2hlcm1lcyU1Q2Zyb250ZW5kJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDbXlfcHJvamVjdCU1Q3dvcmslNUNoZXJtZXMlNUNmcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLDRJQUE0RjtBQUNsSCxzQkFBc0IscVNBQWdGO0FBQ3RHLHNCQUFzQixxU0FBZ0Y7QUFDdEcsc0JBQXNCLDJTQUFtRjtBQUN6RyxzQkFBc0Isa0tBQXdHO0FBQzlILG9CQUFvQiw0SkFBcUc7QUFHdkg7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxteV9wcm9qZWN0XFxcXHdvcmtcXFxcaGVybWVzXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxvYWRpbmcudHN4XCIpO1xuY29uc3QgcGFnZTUgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15X3Byb2plY3RcXFxcd29ya1xcXFxoZXJtZXNcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U1LCBcIkQ6XFxcXG15X3Byb2plY3RcXFxcd29ya1xcXFxoZXJtZXNcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xvYWRpbmcnOiBbbW9kdWxlNCwgXCJEOlxcXFxteV9wcm9qZWN0XFxcXHdvcmtcXFxcaGVybWVzXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbG9hZGluZy50c3hcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxteV9wcm9qZWN0XFxcXHdvcmtcXFxcaGVybWVzXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2Rhc2hib2FyZC9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXNoYm9hcmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(rsc)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Bell,Bot,CheckCircle2,Clock,Loader2,MessageSquare,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sidebar */ \"(ssr)/./components/sidebar.tsx\");\n/* harmony import */ var _components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/mobile-top-nav */ \"(ssr)/./components/mobile-top-nav.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-auth */ \"(ssr)/./hooks/use-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"C端引流\",\n        url: \"https://aihermes.trsb555.com/chat/share?shareId=cQRTQNC001jTvX9J6DpwpclS\",\n        type: \"分析\",\n        status: \"在线\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent2\",\n        name: \"C端转化\",\n        url: \"https://aihermes.trsb555.com/chat/share?shareId=mi9IGmIfVBNBoPqHRTX7Qnw6\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent3\",\n        name: \"B端引流\",\n        url: \"https://aihermes.trsb555.com/chat/share?shareId=aOX3o1JU3r3YnhSIx21DrVyk\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"B端转化\",\n        url: \"https://aihermes.trsb555.com/chat/share?shareId=wQJQ4wBxaXCln8RE2fZ6jx4w\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent5\",\n        name: \"赫尔墨Plus\",\n        url: \"https://aihermes.trsb555.com/chat/share?shareId=xTaEoMWqqfmxD4nnblqbsRUw\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"专家数量\",\n        value: \"5\",\n        subtitle: \"在线:5\",\n        gradient: \"from-blue-500 to-blue-600\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"问题解决率\",\n        value: \"98.2%\",\n        subtitle: \"今日平均\",\n        gradient: \"from-emerald-500 to-emerald-600\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        trend: \"+2.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"平均响应时间\",\n        value: \"5秒\",\n        subtitle: \"较昨日\",\n        gradient: \"from-orange-500 to-orange-600\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        trend: \"-0.3秒\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"今日会话量\",\n        value: \"1,247\",\n        subtitle: \"已处理\",\n        gradient: \"from-purple-500 to-purple-600\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        trend: \"+156\",\n        trendDirection: \"up\"\n    }\n];\nfunction DashboardPage() {\n    const { user, isAuthenticated, isLoading, logout } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPageLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/\");\n                return;\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // Will redirect via useEffect\n        ;\n    }\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"Escape\" && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown);\n                    window.removeEventListener(\"resize\", handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = async ()=>{\n        await logout();\n    // Redirect will be handled by middleware\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-svh bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col md:flex-row overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: user?.user_name || '用户',\n                agents: agents\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: user?.user_name || '用户',\n                agents: agents,\n                mobileMenuOpen: mobileMenuOpen,\n                onToggleMobileMenu: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-4 lg:p-6 overflow-y-auto min-h-0 pt-[73px] md:pt-4\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full min-h-0 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/60 backdrop-blur-sm rounded-2xl p-6 shadow-sm border-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                children: [\n                                                    \"欢迎回来，\",\n                                                    user?.user_name || '用户'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: new Date().toLocaleDateString('zh-CN', {\n                                                    year: 'numeric',\n                                                    month: 'long',\n                                                    day: 'numeric',\n                                                    weekday: 'long'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            {\n                                                icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                label: \"刷新数据\"\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                label: \"通知\"\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                label: \"设置\"\n                                            }\n                                        ].map(({ icon: Icon, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"bg-white/80 border-gray-200 hover:bg-white hover:shadow-md transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    label\n                                                ]\n                                            }, label, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4\",\n                            children: dashboardStats.map((stat, index)=>{\n                                const IconComponent = stat.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-12 h-12 bg-gradient-to-br ${stat.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 23\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-3 border-t border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            stat.trendDirection === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-3 h-3 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `text-sm font-semibold ${stat.trendDirection === \"up\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                                                children: stat.trend\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this) : (()=>{\n                    const selectedAgent = agents.find((agent)=>agent.id === selectedView);\n                    if (selectedAgent) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"h-full bg-transparent border-0 p-0 gap-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"h-full p-0\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 animate-spin text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: [\n                                                    \"加载 \",\n                                                    selectedAgent.name,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 23\n                                }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-12 w-12 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-900 font-medium\",\n                                                        children: \"加载失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-600 text-sm mt-1\",\n                                                        children: [\n                                                            \"无法加载 \",\n                                                            selectedAgent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: ()=>handleViewSelect(selectedView),\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 23\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: selectedAgent.url,\n                                    className: \"w-full h-full border-0 rounded-lg\",\n                                    title: selectedAgent.name,\n                                    onError: ()=>setIframeError(true),\n                                    sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 23\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-full bg-white border-blue-200 p-0 gap-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"h-full p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Bell_Bot_CheckCircle2_Clock_Loader2_MessageSquare_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-16 w-16 text-blue-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                            children: \"功能模块\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-600\",\n                                            children: \"此功能正在开发中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/mobile-top-nav.tsx":
/*!***************************************!*\
  !*** ./components/mobile-top-nav.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileTopNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// 公共样式常量\nconst COMMON_STYLES = {\n    menuButton: \"w-full flex items-center gap-3 px-4 py-3 text-sm font-medium transition-all duration-200\",\n    menuButtonActive: \"bg-blue-600 text-white\",\n    menuButtonInactive: \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\",\n    subMenuButton: \"w-full flex items-center gap-3 px-6 py-3 text-sm font-medium transition-all duration-200\",\n    activeIndicator: \"ml-auto w-2 h-2 bg-white rounded-full\"\n};\n// 状态指示器颜色映射\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"在线\":\n            return \"bg-green-500\";\n        case \"忙碌\":\n            return \"bg-yellow-500\";\n        default:\n            return \"bg-gray-400\";\n    }\n};\nfunction MobileTopNav({ selectedView, onViewSelect, onLogout, username, agents }) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"ai-agents\"\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const toggleSubmenu = (menuId)=>{\n        setExpandedMenus((prev)=>prev.includes(menuId) ? prev.filter((id)=>id !== menuId) : [\n                ...prev,\n                menuId\n            ]);\n    };\n    const handleMenuItemClick = (view)=>{\n        onViewSelect(view);\n        setIsMenuOpen(false) // 点击菜单项后自动收起菜单\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: toggleMenu,\n                            className: \"text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 min-h-[40px] min-w-[40px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `transition-transform duration-200 ${isMenuOpen ? \"rotate-90\" : \"\"}`,\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"智能客服引擎系统 \\xb7 赫尔墨\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden fixed inset-0 z-40 bg-white/20 backdrop-blur-sm\",\n                onClick: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          md:hidden fixed top-14 left-0 right-0 z-50\n          bg-white border-b border-gray-200 shadow-lg\n          transform transition-all duration-300 ease-out\n          ${isMenuOpen ? \"translate-y-0 opacity-100 visible\" : \"-translate-y-full opacity-0 invisible\"}\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[calc(100vh-56px)] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 bg-blue-50 border-b border-blue-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMenuItemClick(\"home\"),\n                                    className: `${COMMON_STYLES.menuButton} ${selectedView === \"home\" ? COMMON_STYLES.menuButtonActive : COMMON_STYLES.menuButtonInactive}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: `h-5 w-5 flex-shrink-0 ${selectedView === \"home\" ? \"text-white\" : \"text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"首页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: COMMON_STYLES.activeIndicator\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 mt-2 pt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleSubmenu(\"ai-agents\"),\n                                            className: \"w-full flex items-center justify-between gap-3 px-4 py-3 text-sm font-medium text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform duration-200 ${expandedMenus.includes(\"ai-agents\") ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `overflow-hidden transition-all duration-300 ${expandedMenus.includes(\"ai-agents\") ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 mt-1\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleMenuItemClick(agent.id),\n                                                        className: `${COMMON_STYLES.subMenuButton} ${selectedView === agent.id ? COMMON_STYLES.menuButtonActive : COMMON_STYLES.menuButtonInactive}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full flex-shrink-0 ${getStatusColor(agent.status)}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: COMMON_STYLES.activeIndicator\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 mt-2 pt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            onLogout();\n                                            setIsMenuOpen(false);\n                                        },\n                                        className: `${COMMON_STYLES.menuButton} ${COMMON_STYLES.menuButtonInactive}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"退出登录\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/mobile-top-nav.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// 公共样式常量\nconst COMMON_STYLES = {\n    button: \"w-full flex items-center gap-3 px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200\",\n    buttonHover: \"hover:bg-blue-50 hover:text-blue-600\",\n    buttonActive: \"bg-blue-600 text-white shadow-sm\",\n    buttonInactive: \"text-gray-600\",\n    tooltip: \"absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto\",\n    tooltipContent: \"bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap\",\n    tooltipArrow: \"before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white\",\n    statusDot: \"w-2 h-2 rounded-full flex-shrink-0\"\n};\n// 辅助函数\nconst getButtonClassName = (isActive, extraClasses = \"\")=>{\n    const baseClasses = `${COMMON_STYLES.button} ${extraClasses}`;\n    return isActive ? `${baseClasses} ${COMMON_STYLES.buttonActive}` : `${baseClasses} ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}`;\n};\nconst getStatusDotClassName = (status)=>{\n    const statusColors = {\n        \"在线\": \"bg-green-500\",\n        \"忙碌\": \"bg-yellow-500\",\n        \"离线\": \"bg-gray-400\"\n    };\n    return `${COMMON_STYLES.statusDot} ${statusColors[status] || statusColors[\"离线\"]}`;\n};\n// 可复用的Tooltip组件\nconst SidebarTooltip = ({ children, text, show })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative\",\n        children: [\n            children,\n            show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: COMMON_STYLES.tooltip,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${COMMON_STYLES.tooltipContent} ${COMMON_STYLES.tooltipArrow}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\nfunction Sidebar({ selectedView, onViewSelect, onLogout, username, agents, mobileMenuOpen, onToggleMobileMenu, onTouchStart, onTouchMove, onTouchEnd }) {\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 默认展开\n    ;\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 响应式侧边栏逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleResize = {\n                \"Sidebar.useEffect.handleResize\": ()=>{\n                    const isLargeScreen = window.innerWidth >= 1280 // xl breakpoint\n                    ;\n                    const isMediumScreen = window.innerWidth >= 1024 // lg breakpoint\n                    ;\n                    if (isLargeScreen) {\n                        // 大屏幕默认展开\n                        setSidebarCollapsed(false);\n                    } else if (isMediumScreen) {\n                        // 中等屏幕默认收起但可手动展开\n                        setSidebarCollapsed(true);\n                    }\n                }\n            }[\"Sidebar.useEffect.handleResize\"];\n            // 初始化时检查屏幕大小\n            handleResize();\n            // 监听窗口大小变化\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"Sidebar.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"Sidebar.useEffect\"];\n        }\n    }[\"Sidebar.useEffect\"], []);\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleSubmenu = (menuId)=>{\n        setExpandedMenus((prev)=>prev.includes(menuId) ? prev.filter((id)=>id !== menuId) : [\n                ...prev,\n                menuId\n            ]);\n    };\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        onToggleMobileMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: `\n          hidden md:flex\n          bg-white\n          border-r border-gray-200\n          flex-col\n          transition-all duration-300 ease-out\n          ${sidebarCollapsed ? \"w-16\" : \"w-64 xl:w-72\"}\n          h-screen\n        `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `border-b border-gray-200 ${sidebarCollapsed ? \"p-2\" : \"p-4\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center mb-3 ${sidebarCollapsed ? \"justify-center\" : \"justify-between\"}`,\n                            children: [\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"智能客服引擎系统 \\xb7 赫尔墨\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: toggleSidebar,\n                                    className: \"text-gray-500 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200\",\n                                    children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 74\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-3 group relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 p-3 bg-blue-50 rounded-lg mb-3 border border-blue-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: username || \"手系 Agent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"管理员\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: `flex-1 ${sidebarCollapsed ? \"p-2 overflow-visible\" : \"p-4 overflow-y-auto\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarTooltip, {\n                                text: \"首页\",\n                                show: sidebarCollapsed,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onViewSelect(\"home\"),\n                                    className: getButtonClassName(selectedView === \"home\", sidebarCollapsed ? \"justify-center\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: `h-5 w-5 flex-shrink-0 ${selectedView === \"home\" ? \"text-white\" : \"text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"首页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 39\n                                        }, this),\n                                        !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative\",\n                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: getButtonClassName(agents.some((agent)=>selectedView === agent.id), \"justify-center cursor-default\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: `h-5 w-5 flex-shrink-0 ${agents.some((agent)=>selectedView === agent.id) ? \"text-white\" : \"text-gray-500\"}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg shadow-xl w-[220px] py-2 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-full top-0 w-2 h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-[-6px] top-4 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1 max-h-[300px] overflow-y-auto overflow-x-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-0.5\",\n                                                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>onViewSelect(agent.id),\n                                                                    className: selectedView === agent.id ? `${COMMON_STYLES.button} rounded-md mx-1 py-2 ${COMMON_STYLES.buttonActive} hover:bg-blue-700 min-w-0` : `${COMMON_STYLES.button} rounded-md mx-1 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 min-w-0`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: getStatusDotClassName(agent.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate flex-1 text-left min-w-0\",\n                                                                            children: agent.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-white rounded-full flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, agent.id, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleSubmenu(\"ai-agents\"),\n                                            className: `${COMMON_STYLES.button} justify-between ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: `h-3 w-3 transition-transform duration-200 ${expandedMenus.includes(\"ai-agents\") ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `overflow-hidden transition-all duration-300 ${expandedMenus.includes(\"ai-agents\") ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5 mt-1\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onViewSelect(agent.id),\n                                                        className: getButtonClassName(selectedView === agent.id, \"min-h-[40px] px-4 py-2.5 ml-2\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: getStatusDotClassName(agent.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 57\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `border-t border-gray-200 ${sidebarCollapsed ? \"p-2\" : \"p-4\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarTooltip, {\n                        text: \"退出登录\",\n                        show: sidebarCollapsed,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            onClick: onLogout,\n                            className: `\n                ${COMMON_STYLES.button} ${COMMON_STYLES.buttonInactive} ${COMMON_STYLES.buttonHover}\n                ${sidebarCollapsed ? \"justify-center\" : \"justify-start\"}\n              `,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.10_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/auth */ \"(ssr)/./lib/api/auth.ts\");\n/* harmony import */ var _lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/error-messages */ \"(ssr)/./lib/utils/error-messages.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            if (response.code === 0) {\n                setUser(response.data);\n            }\n        } catch (error) {\n            console.log('User not authenticated:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        setIsLoading(true);\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.login(credentials);\n            if (response.code === 0) {\n                setUser(response.data);\n            } else {\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(response.msg) || '登录失败');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (credentials)=>{\n        setIsLoading(true);\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.register(credentials);\n            if (response.code === 0) {\n                setUser(response.data);\n            } else {\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(response.msg) || '注册失败');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    const refreshUser = async ()=>{\n        await checkAuth();\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isLoading,\n        login,\n        register,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 83,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-auth.ts":
/*!***************************!*\
  !*** ./hooks/use-auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthenticatedUser: () => (/* binding */ useAuthenticatedUser)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAuth,useAuthenticatedUser auto */ \n\nfunction useAuth() {\n    const context = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    // Auto-refresh user session periodically\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAuth.useEffect\": ()=>{\n            const refreshInterval = setInterval({\n                \"useAuth.useEffect.refreshInterval\": ()=>{\n                    if (context.isAuthenticated) {\n                        context.refreshUser();\n                    }\n                }\n            }[\"useAuth.useEffect.refreshInterval\"], 15 * 60 * 1000) // Refresh every 15 minutes\n            ;\n            return ({\n                \"useAuth.useEffect\": ()=>clearInterval(refreshInterval)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], [\n        context.isAuthenticated,\n        context.refreshUser\n    ]);\n    // Handle page visibility changes\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAuth.useEffect\": ()=>{\n            const handleVisibilityChange = {\n                \"useAuth.useEffect.handleVisibilityChange\": ()=>{\n                    if (document.visibilityState === 'visible' && context.isAuthenticated) {\n                        context.refreshUser();\n                    }\n                }\n            }[\"useAuth.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            return ({\n                \"useAuth.useEffect\": ()=>document.removeEventListener('visibilitychange', handleVisibilityChange)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], [\n        context.isAuthenticated,\n        context.refreshUser\n    ]);\n    return context;\n}\nfunction useAuthenticatedUser() {\n    const { user, isAuthenticated, isLoading } = useAuth();\n    if (isLoading) {\n        return {\n            user: null,\n            isAuthenticated: false,\n            isLoading: true\n        };\n    }\n    if (!isAuthenticated) {\n        throw new Error('User must be authenticated');\n    }\n    return {\n        user: user,\n        isAuthenticated: true,\n        isLoading: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api/auth.ts":
/*!*************************!*\
  !*** ./lib/api/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./lib/api/client.ts\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/validation */ \"(ssr)/./lib/utils/validation.ts\");\n\n\nconst authService = {\n    async login (credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', credentials);\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async register (credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/register', credentials);\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async getCurrentUser () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me');\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async logout () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/logout');\n        return response;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api/client.ts":
/*!***************************!*\
  !*** ./lib/api/client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/error-messages */ \"(ssr)/./lib/utils/error-messages.ts\");\n// Note: This implementation uses native fetch instead of axios to avoid dependency issues\n// You can replace this with axios later if needed\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nclass ApiClient {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n    }\n    async request(endpoint, options = {}, _retry = true) {\n        const url = `${this.baseURL}${endpoint}`;\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers || {}\n            },\n            credentials: 'include',\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            // 优先尝试解析JSON（无论状态码）\n            let result = null;\n            try {\n                result = await response.json();\n            } catch  {\n            // ignore parse error; result 可能为 null\n            }\n            // 非200状态码处理（包含HTTP 401）\n            if (!response.ok) {\n                if (response.status === 401 && _retry && endpoint !== '/auth/refresh') {\n                    const refreshed = await this.refreshAccessToken();\n                    if (refreshed) {\n                        return this.request(endpoint, options, false);\n                    }\n                    if (false) {}\n                }\n                if (result && result.msg) {\n                    throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || `HTTP error! status: ${response.status}`);\n                }\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            // 业务包装：即使是200响应，也需要检查code\n            if (result && typeof result.code !== 'undefined') {\n                if (result.code === 0) {\n                    return result;\n                }\n                if (result.code === 401 && _retry && endpoint !== '/auth/refresh') {\n                    const refreshed = await this.refreshAccessToken();\n                    if (refreshed) {\n                        return this.request(endpoint, options, false);\n                    }\n                    if (false) {}\n                    throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || '请先登录');\n                }\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || '请求失败');\n            }\n            // 无包装时，直接返回解析结果\n            return result;\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.message.includes('登录已过期') || error.message.includes('请先登录') || error.message.includes('401') || error.message.includes('Unauthorized')) {\n                    // Token expired, redirect to login page\n                    if (false) {}\n                }\n                throw error;\n            }\n            throw new Error('Network error occurred');\n        }\n    }\n    async get(endpoint, config = {}) {\n        return this.request(endpoint, {\n            ...config,\n            method: 'GET'\n        });\n    }\n    async post(endpoint, data = {}, config = {}) {\n        return this.request(endpoint, {\n            ...config,\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async put(endpoint, data = {}, config = {}) {\n        return this.request(endpoint, {\n            ...config,\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    }\n    async delete(endpoint, config = {}) {\n        return this.request(endpoint, {\n            ...config,\n            method: 'DELETE'\n        });\n    }\n    // 静默刷新：仅调用一次，成功则更新 httpOnly cookie_access\n    async refreshAccessToken() {\n        try {\n            const url = `${this.baseURL}/auth/refresh`;\n            const resp = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include'\n            });\n            let data = null;\n            try {\n                data = await resp.json();\n            } catch  {\n            // ignore parse error\n            }\n            return !!(resp.ok && data && data.code === 0);\n        } catch  {\n            return false;\n        }\n    }\n}\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcbXlfcHJvamVjdFxcd29ya1xcaGVybWVzXFxmcm9udGVuZFxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils/error-messages.ts":
/*!*************************************!*\
  !*** ./lib/utils/error-messages.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/**\r\n * 错误码与用户友好消息的映射\r\n * \r\n * 这个文件定义了后端API返回的错误码与前端展示的用户友好消息之间的映射关系。\r\n * 后端使用统一的错误码枚举（如ok, invalid_credentials等），前端将其转换为可读性更好的错误消息。\r\n */ /**\r\n * 错误码与用户友好消息的映射对象\r\n */ const ERROR_MESSAGES = {\n    ok: '操作成功',\n    invalid_credentials: '账号或密码错误，请重试',\n    invalid_phone: '手机号格式不正确',\n    invalid_password: '密码格式不正确，密码长度应为8-64位',\n    phone_exists: '该手机号已被注册',\n    unauthorized: '请先登录后再操作',\n    invalid_token: '登录已过期，请重新登录',\n    user_not_found: '用户不存在',\n    internal_error: '服务器内部错误，请稍后再试'\n};\n/**\r\n * 根据错误码获取用户友好的错误消息\r\n * \r\n * @param code 错误码\r\n * @returns 用户友好的错误消息\r\n */ function getErrorMessage(code) {\n    if (!code) return ERROR_MESSAGES.internal_error;\n    return code in ERROR_MESSAGES ? ERROR_MESSAGES[code] : `未知错误 (${code})`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils/error-messages.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils/validation.ts":
/*!*********************************!*\
  !*** ./lib/utils/validation.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeRole: () => (/* binding */ normalizeRole),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   secureStorage: () => (/* binding */ secureStorage),\n/* harmony export */   validateLoginForm: () => (/* binding */ validateLoginForm),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone),\n/* harmony export */   validateRegisterForm: () => (/* binding */ validateRegisterForm)\n/* harmony export */ });\n// Validation utilities using custom validation functions\n// This avoids dependency on zod for now\nconst validatePhone = (phone)=>{\n    if (!phone) return '手机号不能为空';\n    if (!/^1[3-9]\\d{9}$/.test(phone)) return '请输入有效的手机号';\n    return null;\n};\nconst validatePassword = (password)=>{\n    if (!password) return '密码不能为空';\n    if (password.length < 8) return '密码至少8位';\n    if (password.length > 64) return '密码最多64位';\n    if (/\\s/.test(password)) return '密码不能包含空格';\n    return null;\n};\nconst validateLoginForm = (data)=>{\n    const errors = {};\n    const phoneError = validatePhone(data.phone);\n    if (phoneError) errors.phone = phoneError;\n    const passwordError = validatePassword(data.password);\n    if (passwordError) errors.password = passwordError;\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors\n    };\n};\nconst validateRegisterForm = (data)=>{\n    return validateLoginForm(data);\n};\nconst normalizeRole = (role)=>{\n    if (role === 'admin') return 'ADMIN';\n    if (role === 'user') return 'USER';\n    // Default to USER for unknown roles\n    return 'USER';\n};\nconst sanitizeInput = (input)=>{\n    return input.replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript protocol\n    .trim();\n};\nconst secureStorage = {\n    setItem: (key, value)=>{\n        if (false) {}\n    },\n    getItem: (key)=>{\n        if (false) {}\n        return null;\n    },\n    removeItem: (key)=>{\n        if (false) {}\n    },\n    clear: ()=>{\n        if (false) {}\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils/validation.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(ssr)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/lucide-react@0.454.0_react@19.1.1","vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@19.1.10_react@19.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/@radix-ui+react-compose-ref_624bb303a1a4ee376c6bbc8b6a016c46","vendor-chunks/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3","vendor-chunks/clsx@2.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();